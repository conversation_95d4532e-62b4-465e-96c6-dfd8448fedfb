"""
Scheduling system for posting Instagram reels at optimal times
"""
import json
import random
import schedule
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any, Optional
import threading

import pytz

from src.config import Config
from src.utils.logger import logger
from src.message_processor import MessageProcessor, ProcessedMessage
from src.instagram_client import InstagramClient

class PostScheduler:
    """Handles scheduling and posting of Instagram reels"""
    
    def __init__(self):
        self.message_processor = MessageProcessor()
        self.instagram_client = InstagramClient()
        self.schedule_file = Config.BASE_DIR / "post_schedule.json"
        self.scheduled_posts = self._load_schedule()
        self.is_running = False
        self.scheduler_thread = None

        # Tehran timezone for scheduling
        self.tehran_tz = pytz.timezone('Asia/Tehran')

        # Entertainment profile posting schedule (9 AM to 12 PM Tehran time)
        self.posting_start_hour = 9
        self.posting_end_hour = 12
        self.max_daily_posts = 3  # 2-3 posts during the 3-hour window
    
    def _load_schedule(self) -> List[Dict[str, Any]]:
        """Load scheduled posts from file"""
        if self.schedule_file.exists():
            try:
                with open(self.schedule_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('scheduled_posts', [])
            except Exception as e:
                logger.warning(f"Could not load schedule: {e}")
        return []
    
    def _save_schedule(self):
        """Save scheduled posts to file"""
        try:
            data = {
                'scheduled_posts': self.scheduled_posts,
                'last_updated': datetime.now(self.tehran_tz).isoformat()
            }
            with open(self.schedule_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Could not save schedule: {e}")
    
    def _get_optimal_posting_times(self) -> List[int]:
        """
        Get optimal posting times for entertainment profile
        Restricted to 9 AM to 12 PM Tehran time for content creator schedule
        """
        # Entertainment profile posting hours (9 AM to 12 PM Tehran time)
        return [9, 10, 11]  # 9:00 AM, 10:00 AM, 11:00 AM
    
    def _generate_random_post_time(self) -> datetime:
        """Generate a random posting time within 9 AM to 12 PM Tehran time"""
        # Get current time in Tehran timezone
        now_tehran = datetime.now(self.tehran_tz)

        # Check if we're currently in posting window (9 AM to 12 PM)
        current_hour = now_tehran.hour

        # Get optimal hours (9, 10, 11)
        optimal_hours = self._get_optimal_posting_times()

        # Determine target date
        if current_hour < self.posting_start_hour:
            # Before 9 AM today - schedule for today
            target_date = now_tehran.date()
        elif current_hour >= self.posting_end_hour:
            # After 12 PM today - schedule for tomorrow
            target_date = now_tehran.date() + timedelta(days=1)
        else:
            # Currently in posting window - check if we can still post today
            remaining_hours = [h for h in optimal_hours if h > current_hour]
            if remaining_hours and self._can_post_more_today():
                target_date = now_tehran.date()
            else:
                target_date = now_tehran.date() + timedelta(days=1)

        # Choose a random hour from optimal hours
        target_hour = random.choice(optimal_hours)

        # Add randomness to minutes (0-59)
        target_minute = random.randint(0, 59)

        # Create target datetime in Tehran timezone
        target_time = self.tehran_tz.localize(
            datetime.combine(target_date, datetime.min.time().replace(
                hour=target_hour, minute=target_minute
            ))
        )

        # Convert to UTC for storage
        return target_time.astimezone(pytz.UTC).replace(tzinfo=None)

    def _can_post_more_today(self) -> bool:
        """Check if we can post more content today based on daily limits"""
        today_tehran = datetime.now(self.tehran_tz).date()

        # Count posts scheduled/posted for today
        posts_today = 0
        for entry in self.scheduled_posts:
            try:
                # Convert scheduled time to Tehran timezone for comparison
                scheduled_utc = datetime.fromisoformat(entry['scheduled_time'])
                scheduled_tehran = pytz.UTC.localize(scheduled_utc).astimezone(self.tehran_tz)

                if scheduled_tehran.date() == today_tehran:
                    posts_today += 1
            except Exception:
                continue

        return posts_today < self.max_daily_posts

    def _get_next_available_slot(self) -> datetime:
        """Get the next available posting slot for optimal distribution"""
        today_tehran = datetime.now(self.tehran_tz).date()

        # Get all scheduled times for today
        today_slots = []
        for entry in self.scheduled_posts:
            try:
                scheduled_utc = datetime.fromisoformat(entry['scheduled_time'])
                scheduled_tehran = pytz.UTC.localize(scheduled_utc).astimezone(self.tehran_tz)

                if scheduled_tehran.date() == today_tehran:
                    today_slots.append(scheduled_tehran.hour)
            except Exception:
                continue

        # Find available slots (9, 10, 11)
        available_hours = [h for h in self._get_optimal_posting_times() if h not in today_slots]

        if available_hours:
            # Use today if slots available
            target_hour = min(available_hours)  # Use earliest available slot
            target_minute = random.randint(0, 59)

            target_time = self.tehran_tz.localize(
                datetime.combine(today_tehran, datetime.min.time().replace(
                    hour=target_hour, minute=target_minute
                ))
            )

            # If the time has passed, move to tomorrow
            now_tehran = datetime.now(self.tehran_tz)
            if target_time <= now_tehran:
                tomorrow = today_tehran + timedelta(days=1)
                target_hour = random.choice(self._get_optimal_posting_times())
                target_time = self.tehran_tz.localize(
                    datetime.combine(tomorrow, datetime.min.time().replace(
                        hour=target_hour, minute=target_minute
                    ))
                )
        else:
            # Schedule for tomorrow
            tomorrow = today_tehran + timedelta(days=1)
            target_hour = random.choice(self._get_optimal_posting_times())
            target_minute = random.randint(0, 59)

            target_time = self.tehran_tz.localize(
                datetime.combine(tomorrow, datetime.min.time().replace(
                    hour=target_hour, minute=target_minute
                ))
            )

        return target_time.astimezone(pytz.UTC).replace(tzinfo=None)

    def schedule_post(self, processed_message: ProcessedMessage) -> bool:
        """
        Schedule a processed message for posting
        
        Args:
            processed_message: The message to schedule
            
        Returns:
            True if scheduled successfully, False otherwise
        """
        try:
            if not processed_message.video_generated or not processed_message.output_video_path:
                logger.error("Cannot schedule post: video not generated")
                return False
            
            # Generate optimal posting time with better distribution
            post_time = self._get_next_available_slot()
            
            # Create schedule entry
            schedule_entry = {
                'id': f"post_{processed_message.telegram_message.message_id}_{int(time.time())}",
                'message_id': processed_message.telegram_message.message_id,
                'video_path': str(processed_message.output_video_path),
                'caption': processed_message.text_content,
                'scheduled_time': post_time.isoformat(),
                'status': 'scheduled',
                'created_at': datetime.now(self.tehran_tz).isoformat(),
                'attempts': 0,
                'max_attempts': 3
            }
            
            self.scheduled_posts.append(schedule_entry)
            self._save_schedule()
            
            logger.info(f"Scheduled post for {post_time.strftime('%Y-%m-%d %H:%M:%S')}: {processed_message.telegram_message.message_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error scheduling post: {e}")
            return False
    
    def schedule_ready_posts(self) -> int:
        """
        Schedule all posts that are ready for posting
        
        Returns:
            Number of posts scheduled
        """
        try:
            ready_messages = self.message_processor.get_ready_for_posting()
            scheduled_count = 0
            
            for message in ready_messages:
                if self.schedule_post(message):
                    scheduled_count += 1
            
            logger.info(f"Scheduled {scheduled_count} posts")
            return scheduled_count
            
        except Exception as e:
            logger.error(f"Error scheduling ready posts: {e}")
            return 0
    
    def _execute_scheduled_post(self, schedule_entry: Dict[str, Any]) -> bool:
        """Execute a scheduled post"""
        try:
            video_path = Path(schedule_entry['video_path'])
            caption = schedule_entry['caption']
            
            logger.info(f"Executing scheduled post: {schedule_entry['id']}")

            success = False
            media_id = None

            # Post to Instagram (if enabled)
            if Config.ENABLE_INSTAGRAM_POSTING:
                media_id = self.instagram_client.post_reel(video_path, caption)
                if media_id:
                    logger.info(f"Successfully posted to Instagram: {media_id}")
                    success = True
                else:
                    logger.warning("Instagram posting failed")
                    success = False
            else:
                logger.info("Instagram posting disabled, skipping")
                success = False  # Don't mark as posted if Instagram posting is disabled

            # Send video to Telegram (if enabled)
            if Config.ENABLE_TELEGRAM_VIDEO_SENDING:
                try:
                    import asyncio
                    from .telegram_client import TelegramClient

                    # Create Telegram client for sending
                    telegram_client = TelegramClient()

                    # Create caption for Telegram
                    telegram_caption = f"📹 <b>New Video Generated</b>\n\n{caption}\n\n🔗 @linkychannell"

                    # Send video
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    telegram_success = loop.run_until_complete(
                        telegram_client.send_video(video_path, telegram_caption)
                    )
                    loop.close()

                    if telegram_success:
                        logger.info("Successfully sent video to Telegram")
                    else:
                        logger.warning("Failed to send video to Telegram")

                except Exception as e:
                    logger.error(f"Error sending video to Telegram: {e}")

            if success:
                # Update schedule entry
                schedule_entry['status'] = 'posted'
                schedule_entry['posted_at'] = datetime.now(self.tehran_tz).isoformat()
                if media_id:
                    schedule_entry['media_id'] = media_id

                # Mark message as posted
                message_id = schedule_entry['message_id']
                for message in self.message_processor.processing_queue:
                    if message.telegram_message.message_id == message_id:
                        self.message_processor.mark_instagram_posted(message)
                        break

                logger.info(f"Successfully executed scheduled post: {schedule_entry['id']}")
                return True
            else:
                # Update attempt count
                schedule_entry['attempts'] += 1
                schedule_entry['last_attempt'] = datetime.now(self.tehran_tz).isoformat()

                if schedule_entry['attempts'] >= schedule_entry['max_attempts']:
                    schedule_entry['status'] = 'failed'
                    logger.error(f"Post failed after {schedule_entry['max_attempts']} attempts: {schedule_entry['id']}")
                else:
                    # Reschedule for next posting window
                    new_time = self._generate_random_post_time()
                    schedule_entry['scheduled_time'] = new_time.isoformat()
                    logger.warning(f"Post failed, rescheduled for {new_time}: {schedule_entry['id']}")
                
                return False
                
        except Exception as e:
            logger.error(f"Error executing scheduled post: {e}")
            schedule_entry['attempts'] += 1
            schedule_entry['last_error'] = str(e)
            schedule_entry['last_attempt'] = datetime.now(self.tehran_tz).isoformat()

            if schedule_entry['attempts'] >= schedule_entry['max_attempts']:
                schedule_entry['status'] = 'failed'

            return False
    
    def check_and_execute_posts(self):
        """Check for posts that should be executed now"""
        try:
            now_utc = datetime.utcnow()
            now_tehran = pytz.UTC.localize(now_utc).astimezone(self.tehran_tz)
            executed_count = 0

            # Only execute posts during posting hours (9 AM to 12 PM Tehran time)
            if not (self.posting_start_hour <= now_tehran.hour < self.posting_end_hour):
                return

            for schedule_entry in self.scheduled_posts:
                if schedule_entry['status'] != 'scheduled':
                    continue

                scheduled_time = datetime.fromisoformat(schedule_entry['scheduled_time'])

                if now_utc >= scheduled_time:
                    if self._execute_scheduled_post(schedule_entry):
                        executed_count += 1

            if executed_count > 0:
                self._save_schedule()
                logger.info(f"Executed {executed_count} scheduled posts")

        except Exception as e:
            logger.error(f"Error checking and executing posts: {e}")
    
    def start_scheduler(self):
        """Start the scheduler in a separate thread"""
        if self.is_running:
            logger.warning("Scheduler is already running")
            return
        
        self.is_running = True
        
        # Schedule the check function to run every minute
        schedule.every(1).minutes.do(self.check_and_execute_posts)
        
        def run_scheduler():
            logger.info("Post scheduler started")
            while self.is_running:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
            logger.info("Post scheduler stopped")
        
        self.scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
        self.scheduler_thread.start()
    
    def stop_scheduler(self):
        """Stop the scheduler"""
        self.is_running = False
        if self.scheduler_thread:
            self.scheduler_thread.join(timeout=5)
        logger.info("Post scheduler stopped")
    
    def get_schedule_status(self) -> Dict[str, Any]:
        """Get current schedule status"""
        try:
            total_scheduled = len([p for p in self.scheduled_posts if p['status'] == 'scheduled'])
            total_posted = len([p for p in self.scheduled_posts if p['status'] == 'posted'])
            total_failed = len([p for p in self.scheduled_posts if p['status'] == 'failed'])
            
            # Get next scheduled post
            next_post = None
            next_post_time = None
            
            scheduled_posts = [p for p in self.scheduled_posts if p['status'] == 'scheduled']
            if scheduled_posts:
                scheduled_posts.sort(key=lambda x: x['scheduled_time'])
                next_post = scheduled_posts[0]
                next_post_time = datetime.fromisoformat(next_post['scheduled_time'])
            
            return {
                'is_running': self.is_running,
                'total_scheduled': total_scheduled,
                'total_posted': total_posted,
                'total_failed': total_failed,
                'next_post_time': next_post_time.isoformat() if next_post_time else None,
                'next_post_id': next_post['id'] if next_post else None
            }
            
        except Exception as e:
            logger.error(f"Error getting schedule status: {e}")
            return {}
    
    def cleanup_old_entries(self, days_old: int = 30):
        """Clean up old schedule entries"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_old)
            
            original_count = len(self.scheduled_posts)
            
            self.scheduled_posts = [
                entry for entry in self.scheduled_posts
                if datetime.fromisoformat(entry['created_at']) > cutoff_date
                or entry['status'] == 'scheduled'  # Keep all scheduled posts regardless of age
            ]
            
            removed_count = original_count - len(self.scheduled_posts)
            
            if removed_count > 0:
                self._save_schedule()
                logger.info(f"Cleaned up {removed_count} old schedule entries")
            
            return removed_count
            
        except Exception as e:
            logger.error(f"Error cleaning up old entries: {e}")
            return 0
