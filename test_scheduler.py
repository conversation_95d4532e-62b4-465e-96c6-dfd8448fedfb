#!/usr/bin/env python3
"""
Test script for the new Tehran timezone scheduler
"""
import sys
from pathlib import Path
from datetime import datetime
import pytz

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from src.scheduler import PostScheduler
from src.utils.logger import logger

def test_timezone_functionality():
    """Test Tehran timezone functionality"""
    print("🕐 Testing Tehran timezone functionality...")
    
    scheduler = PostScheduler()
    
    # Test timezone setup
    tehran_tz = scheduler.tehran_tz
    now_tehran = datetime.now(tehran_tz)
    
    print(f"📍 Current Tehran time: {now_tehran.strftime('%Y-%m-%d %H:%M:%S %Z')}")
    print(f"⏰ Current hour in Tehran: {now_tehran.hour}")
    
    # Test posting window
    in_posting_window = scheduler.posting_start_hour <= now_tehran.hour < scheduler.posting_end_hour
    print(f"🎯 Currently in posting window (9 AM - 12 PM): {in_posting_window}")
    
    # Test optimal posting times
    optimal_times = scheduler._get_optimal_posting_times()
    print(f"📅 Optimal posting hours: {optimal_times}")
    
    return True

def test_post_scheduling():
    """Test post scheduling logic"""
    print("\n📝 Testing post scheduling logic...")
    
    scheduler = PostScheduler()
    
    # Test next available slot
    try:
        next_slot = scheduler._get_next_available_slot()
        next_slot_tehran = pytz.UTC.localize(next_slot).astimezone(scheduler.tehran_tz)
        
        print(f"⏭️  Next available posting slot: {next_slot_tehran.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        print(f"🎯 Slot hour: {next_slot_tehran.hour}")
        
        # Verify it's within posting window
        if next_slot_tehran.hour in scheduler._get_optimal_posting_times():
            print("✅ Slot is within optimal posting hours")
        else:
            print("❌ Slot is NOT within optimal posting hours")
            
        return True
    except Exception as e:
        print(f"❌ Error testing post scheduling: {e}")
        return False

def test_daily_limits():
    """Test daily posting limits"""
    print("\n📊 Testing daily posting limits...")
    
    scheduler = PostScheduler()
    
    can_post = scheduler._can_post_more_today()
    print(f"📈 Can post more today: {can_post}")
    print(f"🎯 Max daily posts: {scheduler.max_daily_posts}")
    
    return True

def main():
    """Run all tests"""
    print("🚀 Starting scheduler tests...\n")
    
    tests = [
        ("Tehran Timezone", test_timezone_functionality),
        ("Post Scheduling", test_post_scheduling),
        ("Daily Limits", test_daily_limits),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"{status}")
        except Exception as e:
            print(f"❌ FAILED: {e}")
            results.append((test_name, False))
    
    print("\n" + "="*50)
    print("📋 TEST RESULTS:")
    print("="*50)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    all_passed = all(result for _, result in results)
    
    if all_passed:
        print("\n🎉 All tests passed! Scheduler is ready for Tehran timezone posting.")
    else:
        print("\n⚠️  Some tests failed. Please check the implementation.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
